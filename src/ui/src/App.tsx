import React, { useEffect, useState } from "react";
import { SubscriptionManager } from "./components/SubscriptionManager";
import { NodeList } from "./components/NodeList";
import { TestResults } from "./components/TestResults";
import { ConfigGenerator } from "./components/ConfigGenerator";
import { ApiService } from "./services/api";
import "./App.css";

interface ProxyNode {
  id: string;
  name: string;
  type: string;
  server: string;
  port: number;
  testResult?: {
    connectivity: boolean;
    latency: number;
    downloadSpeed: number;
    uploadSpeed: number;
    timestamp: number;
    error?: string;
  };
}

function App() {
  const [activeTab, setActiveTab] = useState("subscription");
  const [nodes, setNodes] = useState<ProxyNode[]>([]);
  const [selectedNodes, setSelectedNodes] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  // 加载节点列表
  const loadNodes = async () => {
    try {
      setLoading(true);
      const response = await ApiService.getNodes();
      if (response.success) {
        setNodes(response.data || []);
      }
    } catch (error) {
      console.error("加载节点失败:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadNodes();
  }, []);

  const handleNodesUpdate = () => {
    loadNodes();
  };

  const handleNodeSelect = (nodeIds: string[]) => {
    setSelectedNodes(nodeIds);
  };

  return (
    <div className="app">
      <header className="app-header">
        <h1>🚀 Sub Node Manage</h1>
        <p>基于mihomo内核的代理订阅管理工具</p>
      </header>

      <nav className="app-nav">
        <button
          type="button"
          className={activeTab === "subscription" ? "active" : ""}
          onClick={() => setActiveTab("subscription")}
        >
          📡 订阅管理
        </button>
        <button
          type="button"
          className={activeTab === "nodes" ? "active" : ""}
          onClick={() => setActiveTab("nodes")}
        >
          🌐 节点列表
        </button>
        <button
          type="button"
          className={activeTab === "test" ? "active" : ""}
          onClick={() => setActiveTab("test")}
        >
          🔍 测试结果
        </button>
        <button
          type="button"
          className={activeTab === "config" ? "active" : ""}
          onClick={() => setActiveTab("config")}
        >
          ⚙️ 配置生成
        </button>
      </nav>

      <main className="app-main">
        {activeTab === "subscription" && <SubscriptionManager onNodesUpdate={handleNodesUpdate} />}

        {activeTab === "nodes" && (
          <NodeList
            nodes={nodes}
            selectedNodes={selectedNodes}
            onNodeSelect={handleNodeSelect}
            onNodesUpdate={handleNodesUpdate}
            loading={loading}
          />
        )}

        {activeTab === "test" && <TestResults nodes={nodes} />}

        {activeTab === "config" && (
          <ConfigGenerator
            nodes={nodes}
            selectedNodes={selectedNodes}
          />
        )}
      </main>
    </div>
  );
}

export default App;
