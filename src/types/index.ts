// 代理节点类型定义

export interface ProxyNode {
  id: string;
  name: string;
  type: "vmess" | "vless" | "trojan" | "ss" | "ssr";
  server: string;
  port: number;
  uuid?: string;
  password?: string;
  cipher?: string;
  network?: string;
  tls?: boolean;
  sni?: string;
  alpn?: string[];
  path?: string;
  host?: string;
  headers?: Record<string, string>;
  // 其他协议特定字段
  [key: string]: unknown;
}

export interface TestResult {
  nodeId: string;
  timestamp: number;
  connectivity: boolean;
  latency: number; // ms，-1表示超时
  downloadSpeed: number; // KB/s，-1表示测试失败
  uploadSpeed: number; // KB/s，-1表示测试失败
  error?: string;
}

export interface Subscription {
  id: string;
  name: string;
  url: string;
  lastUpdate: number;
  nodeCount: number;
  isActive: boolean;
}

export interface TestConfig {
  timeout: number; // 超时时间（秒）
  concurrency: number; // 并发数
  testUrl: string; // 测试URL
  speedTestSize: number; // 速度测试文件大小（KB）
}

export interface MihomoConfig {
  port?: number;
  "socks-port"?: number;
  "redir-port"?: number;
  "tproxy-port"?: number;
  "mixed-port"?: number;
  "allow-lan"?: boolean;
  "bind-address"?: string;
  mode?: "rule" | "global" | "direct";
  "log-level"?: "info" | "warning" | "error" | "debug" | "silent";
  "external-controller"?: string;
  "external-ui"?: string;
  secret?: string;
  dns?: Record<string, unknown>;
  proxies?: ProxyNode[];
  "proxy-groups"?: Record<string, unknown>[];
  rules?: string[];
  [key: string]: unknown;
}

export interface FilterOptions {
  minLatency?: number;
  maxLatency?: number;
  minSpeed?: number;
  countries?: string[];
  protocols?: string[];
  keywords?: string[];
}

export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface LogEntry {
  timestamp: number;
  level: "info" | "warn" | "error" | "debug";
  message: string;
  module?: string;
  data?: Record<string, unknown>;
}
